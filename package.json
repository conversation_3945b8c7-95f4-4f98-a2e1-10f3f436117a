{"name": "commune", "version": "0.0.1", "private": true, "workspaces": ["libs/api", "backend", "frontend"], "scripts": {"frontend:dev": "npm run dev --workspace=frontend", "backend:dev": "npm run dev --workspace=backend", "dev": "concurrently \"npm run backend:dev\" \"npm run frontend:dev\"", "api:build": "npm run build --workspace=libs/api"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/heapdump": "^0.3.4", "@types/object-inspect": "^1.13.0", "@types/sanitize-html": "^2.16.0", "typescript": "^5.8.3"}, "dependencies": {"@nestjs/schedule": "^6.0.0", "@ocelotjungle/case-converters": "^1.0.0", "@tinymce/tinymce-svelte": "^3.1.0", "cors": "^2.8.5", "cropperjs": "^1.6.2", "heapdump": "^0.3.15", "node-cron": "^4.2.1", "object-inspect": "^1.13.4", "sanitize-html": "^2.17.0", "superjson": "^2.2.2", "tinymce": "^7.9.1", "tsup": "^8.5.0", "zod": "^3.25.76"}}