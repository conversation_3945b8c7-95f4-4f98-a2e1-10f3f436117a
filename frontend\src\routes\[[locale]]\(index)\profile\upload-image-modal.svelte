<script lang="ts">
  import type { Common } from "@commune/api";
  import { onDestroy } from "svelte";
  import Cropper from "cropperjs";
  import "cropperjs/dist/cropper.css";

  import { Consts } from "@commune/api";
  import { fetchWithAuth } from "$lib";
  import { Modal } from "$lib/components";

  interface Props {
    locale: Common.WebsiteLocale;
    show: boolean;
    onHide: () => void;
    userId: string;
    onImageUploaded: () => void;
  }

  const MAX_FILE_SIZE_MB = Consts.MAX_IMAGE_FILE_SIZE / (1024 * 1024);

  const i18n = {
    en: {
      uploadImage: "Upload Profile Image",
      upload: "Upload",
      cancel: "Cancel",
      back: "Back",
      next: "Next",
      uploading: "Uploading...",
      imageUploadedSuccessfully: "Image uploaded successfully!",
      pleaseSelectImage: "Please select an image to upload",
      invalidFileTypeError: "Invalid file type. Please upload a JPG, PNG, or WebP image.",
      fileTooLarge: `File is too large. Maximum size is ${MAX_FILE_SIZE_MB}MB.`,
      failedToUploadImage: "Failed to upload image",
      errorOccurred: "An error occurred while uploading the image",
      uploadImageMaxSize: `Upload an image (JPG, PNG, WebP), max ${MAX_FILE_SIZE_MB}MB.`,
      editImage: "Edit Image",
      crop: "Crop",
      rotateLeft: "Rotate Left",
      rotateRight: "Rotate Right",
      flipHorizontal: "Flip Horizontal",
      flipVertical: "Flip Vertical",
      reset: "Reset",
      zoomIn: "Zoom In",
      zoomOut: "Zoom Out",
      aspectRatio: "Aspect Ratio",
      free: "Free",
      square: "Square (1:1)",
      portrait: "Portrait (3:4)",
      landscape: "Landscape (4:3)",
    },

    ru: {
      uploadImage: "Загрузить изображение профиля",
      upload: "Загрузить",
      cancel: "Отменить",
      back: "Назад",
      next: "Далее",
      uploading: "Загрузка...",
      imageUploadedSuccessfully: "Изображение загружено успешно!",
      pleaseSelectImage: "Пожалуйста, выберите изображение для загрузки",
      invalidFileTypeError:
        "Неверный тип файла. Пожалуйста, загрузите JPG, PNG или WebP изображение.",
      fileTooLarge: `Файл слишком большой. Максимальный размер - ${MAX_FILE_SIZE_MB}MB.`,
      failedToUploadImage: "Не удалось загрузить изображение",
      errorOccurred: "Произошла ошибка при загрузке изображения",
      uploadImageMaxSize: `Загрузите изображение (JPG, PNG, WebP), максимальный размер - ${MAX_FILE_SIZE_MB}MB.`,
      editImage: "Редактировать изображение",
      crop: "Обрезать",
      rotateLeft: "Повернуть влево",
      rotateRight: "Повернуть вправо",
      flipHorizontal: "Отразить горизонтально",
      flipVertical: "Отразить вертикально",
      reset: "Сбросить",
      zoomIn: "Увеличить",
      zoomOut: "Уменьшить",
      aspectRatio: "Соотношение сторон",
      free: "Свободное",
      square: "Квадрат (1:1)",
      portrait: "Портрет (3:4)",
      landscape: "Альбом (4:3)",
    },
  };

  const { locale, show, onHide, userId, onImageUploaded }: Props = $props();

  const t = $derived(i18n[locale]);

  // Component state
  let selectedFile = $state<File | null>(null);
  let previewUrl = $state<string | null>(null);
  let error = $state("");
  let isSubmitting = $state(false);
  let submitSuccess = $state(false);
  let showEditor = $state(false);
  let editedImageBlob = $state<Blob | null>(null);

  // Cropper instance and DOM references
  let cropper: Cropper | null = null;
  let cropperImage: HTMLImageElement | null = null;
  let currentAspectRatio = $state<number | undefined>(undefined);

  // Aspect ratio options - using derived to handle locale changes
  const aspectRatios = $derived([
    { label: t.free, value: undefined },
    { label: t.square, value: 1 },
    { label: t.portrait, value: 3 / 4 },
    { label: t.landscape, value: 4 / 3 },
  ]);

  // Lifecycle management
  onDestroy(() => {
    if (cropper) {
      cropper.destroy();
      cropper = null;
    }
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
  });

  const handleFileChange = (e: Event) => {
    const target = e.target as HTMLInputElement;
    const files = target.files;

    error = "";
    showEditor = false;
    editedImageBlob = null;

    if (!files || files.length === 0) {
      selectedFile = null;
      previewUrl = null;
      return;
    }

    const file = files[0];

    // Validate file type
    if (!Consts.ALLOWED_IMAGE_FILE_TYPES.includes(file.type)) {
      error = t.invalidFileTypeError;
      selectedFile = null;
      previewUrl = null;
      target.value = "";
      return;
    }

    // Validate file size
    if (file.size > Consts.MAX_IMAGE_FILE_SIZE) {
      error = t.fileTooLarge;
      selectedFile = null;
      previewUrl = null;
      target.value = "";
      return;
    }

    selectedFile = file;

    // Create preview URL
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
    const objectUrl = URL.createObjectURL(file);
    previewUrl = objectUrl;
  };

  const handleEditImage = () => {
    if (!previewUrl) return;
    showEditor = true;

    // Initialize cropper after DOM update
    setTimeout(() => {
      initializeCropper();
    }, 100);
  };

  const initializeCropper = () => {
    if (!cropperImage || !previewUrl) return;

    // Destroy existing cropper
    if (cropper) {
      cropper.destroy();
    }

    cropper = new Cropper(cropperImage, {
      aspectRatio: currentAspectRatio,
      viewMode: 1,
      dragMode: "move",
      autoCropArea: 0.8,
      restore: false,
      guides: true,
      center: true,
      highlight: false,
      cropBoxMovable: true,
      cropBoxResizable: true,
      toggleDragModeOnDblclick: false,
    });
  };

  // Cropper control functions
  const rotateLeft = () => cropper?.rotate(-90);
  const rotateRight = () => cropper?.rotate(90);
  const flipHorizontal = () => cropper?.scaleX(cropper.getData().scaleX === 1 ? -1 : 1);
  const flipVertical = () => cropper?.scaleY(cropper.getData().scaleY === 1 ? -1 : 1);
  const zoomIn = () => cropper?.zoom(0.1);
  const zoomOut = () => cropper?.zoom(-0.1);
  const resetCropper = () => cropper?.reset();

  const setAspectRatio = (ratio: number | undefined) => {
    currentAspectRatio = ratio;
    if (cropper) {
      cropper.setAspectRatio(ratio || NaN);
    }
  };

  const handleBackToSelection = () => {
    showEditor = false;
    if (cropper) {
      cropper.destroy();
      cropper = null;
    }
  };

  const handleApplyEdit = () => {
    if (!cropper) return;

    // Get cropped canvas
    const canvas = cropper.getCroppedCanvas({
      width: 800,
      height: 800,
      imageSmoothingEnabled: true,
      imageSmoothingQuality: "high",
    });

    // Convert to blob
    canvas.toBlob(
      (blob) => {
        if (blob) {
          editedImageBlob = blob;
          showEditor = false;
          if (cropper) {
            cropper.destroy();
            cropper = null;
          }
        }
      },
      "image/jpeg",
      0.9,
    );
  };

  const handleSubmit = async () => {
    if (!selectedFile && !editedImageBlob) {
      error = t.pleaseSelectImage;
      return;
    }

    isSubmitting = true;
    error = "";

    try {
      const formData = new FormData();

      // Use edited image blob if available, otherwise use original file
      const fileToUpload = editedImageBlob || selectedFile;
      if (!fileToUpload) {
        throw new Error(t.pleaseSelectImage);
      }

      // Create a File object from blob if needed
      const finalFile = editedImageBlob
        ? new File([editedImageBlob], selectedFile?.name || "edited-image.jpg", {
            type: "image/jpeg",
          })
        : selectedFile!;

      formData.append("image", finalFile);

      const response = await fetchWithAuth(`/api/user/${userId}/image`, {
        method: "PUT",
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || t.failedToUploadImage);
      }

      submitSuccess = true;
      onImageUploaded();

      const imageInput = document.getElementById("imageInput") as HTMLInputElement | null;
      if (imageInput) {
        imageInput.value = "";
      }

      // Close modal after a short delay
      setTimeout(() => {
        handleClose();
      }, 1500);
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorOccurred;
      console.error(err);
    } finally {
      isSubmitting = false;
    }
  };

  const handleClose = () => {
    selectedFile = null;
    previewUrl = null;
    editedImageBlob = null;
    error = "";
    submitSuccess = false;
    showEditor = false;
    currentAspectRatio = undefined;

    if (cropper) {
      cropper.destroy();
      cropper = null;
    }

    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }

    onHide();
  };
</script>

<Modal
  {show}
  title={showEditor ? t.editImage : t.uploadImage}
  onClose={handleClose}
  onSubmit={showEditor ? handleApplyEdit : handleSubmit}
  submitText={showEditor ? t.next : isSubmitting ? t.uploading : t.upload}
  cancelText={showEditor ? t.back : t.cancel}
  submitDisabled={showEditor ? !cropper : (!selectedFile && !editedImageBlob) || isSubmitting}
  cancelDisabled={isSubmitting}
  {isSubmitting}
  size="xl"
  showSubmitButton={!showEditor || !!cropper}
>
  {#if submitSuccess}
    <div class="alert alert-success mb-3">
      {t.imageUploadedSuccessfully}
    </div>
  {/if}

  {#if error}
    <div class="alert alert-danger mb-3">
      {error}
    </div>
  {/if}

  {#if !showEditor}
    <!-- File Selection View -->
    <form>
      <div class="mb-3">
        <label for="imageInput" class="form-label">{t.pleaseSelectImage}</label>
        <input
          id="imageInput"
          type="file"
          class="form-control"
          accept=".jpg,.jpeg,.png,.webp"
          onchange={handleFileChange}
          disabled={isSubmitting}
        />
        <p class="form-text text-muted">
          {t.uploadImageMaxSize}
        </p>

        {#if previewUrl}
          <div class="mt-3 text-center">
            <img src={previewUrl} alt="Preview" class="img-thumbnail" style:max-height="200px" />
            <div class="mt-2">
              <button
                type="button"
                class="btn btn-outline-primary btn-sm"
                onclick={handleEditImage}
                disabled={isSubmitting}
              >
                <i class="bi bi-pencil"></i>
                {t.editImage}
              </button>
            </div>
          </div>
        {/if}

        {#if editedImageBlob}
          <div class="mt-3 text-center">
            <div class="alert alert-info">
              <i class="bi bi-check-circle"></i>
              Image has been edited and is ready for upload.
            </div>
          </div>
        {/if}
      </div>
    </form>
  {:else}
    <!-- Image Editor View -->
    <div class="image-editor-container">
      <!-- Cropper Controls -->
      <div class="editor-controls mb-3">
        <div class="row g-2">
          <!-- Aspect Ratio -->
          <div class="col-md-3">
            <label class="form-label small">{t.aspectRatio}</label>
            <select
              class="form-select form-select-sm"
              bind:value={currentAspectRatio}
              onchange={() => setAspectRatio(currentAspectRatio)}
            >
              {#each aspectRatios as ratio}
                <option value={ratio.value}>{ratio.label}</option>
              {/each}
            </select>
          </div>

          <!-- Rotation Controls -->
          <div class="col-md-3">
            <label class="form-label small">{t.crop}</label>
            <div class="btn-group w-100" role="group">
              <button
                type="button"
                class="btn btn-outline-secondary btn-sm"
                onclick={rotateLeft}
                title={t.rotateLeft}
              >
                <i class="bi bi-arrow-counterclockwise"></i>
              </button>
              <button
                type="button"
                class="btn btn-outline-secondary btn-sm"
                onclick={rotateRight}
                title={t.rotateRight}
              >
                <i class="bi bi-arrow-clockwise"></i>
              </button>
            </div>
          </div>

          <!-- Flip Controls -->
          <div class="col-md-3">
            <label class="form-label small">Flip</label>
            <div class="btn-group w-100" role="group">
              <button
                type="button"
                class="btn btn-outline-secondary btn-sm"
                onclick={flipHorizontal}
                title={t.flipHorizontal}
              >
                <i class="bi bi-arrow-left-right"></i>
              </button>
              <button
                type="button"
                class="btn btn-outline-secondary btn-sm"
                onclick={flipVertical}
                title={t.flipVertical}
              >
                <i class="bi bi-arrow-up-down"></i>
              </button>
            </div>
          </div>

          <!-- Zoom and Reset -->
          <div class="col-md-3">
            <label class="form-label small">Tools</label>
            <div class="btn-group w-100" role="group">
              <button
                type="button"
                class="btn btn-outline-secondary btn-sm"
                onclick={zoomOut}
                title={t.zoomOut}
              >
                <i class="bi bi-zoom-out"></i>
              </button>
              <button
                type="button"
                class="btn btn-outline-secondary btn-sm"
                onclick={zoomIn}
                title={t.zoomIn}
              >
                <i class="bi bi-zoom-in"></i>
              </button>
              <button
                type="button"
                class="btn btn-outline-warning btn-sm"
                onclick={resetCropper}
                title={t.reset}
              >
                <i class="bi bi-arrow-clockwise"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Cropper Container -->
      <div class="cropper-container">
        {#if previewUrl}
          <img
            bind:this={cropperImage}
            src={previewUrl}
            alt="Image to crop"
            class="cropper-image"
          />
        {/if}
      </div>
    </div>
  {/if}
</Modal>

<style>
  .image-editor-container {
    max-height: 70vh;
    overflow: hidden;
  }

  .editor-controls {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
  }

  .editor-controls .form-label {
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: #495057;
  }

  .cropper-container {
    height: 400px;
    width: 100%;
    position: relative;
    overflow: hidden;
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
  }

  .cropper-image {
    max-width: 100%;
    height: auto;
    display: block;
  }

  /* Custom cropper styling */
  :global(.cropper-container) {
    direction: ltr;
    font-size: 0;
    line-height: 0;
    position: relative;
    touch-action: none;
    user-select: none;
  }

  :global(.cropper-container img) {
    display: block;
    height: 100%;
    image-orientation: 0deg;
    max-height: none;
    max-width: none;
    min-height: 0;
    min-width: 0;
    width: 100%;
  }

  /* Enhance button groups */
  .btn-group .btn {
    border-radius: 0;
  }

  .btn-group .btn:first-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
  }

  .btn-group .btn:last-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .editor-controls .row {
      gap: 0.5rem;
    }

    .editor-controls .col-md-3 {
      flex: 0 0 100%;
      max-width: 100%;
      margin-bottom: 0.5rem;
    }

    .cropper-container {
      height: 300px;
    }
  }

  /* Loading state */
  .image-editor-container.loading {
    opacity: 0.7;
    pointer-events: none;
  }

  /* Success indicator styling */
  .alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
  }
</style>
